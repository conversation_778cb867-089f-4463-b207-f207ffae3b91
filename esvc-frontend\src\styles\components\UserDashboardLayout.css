/* User Dashboard Layout Styles */
.user-dashboard-layout-container {
  min-height: 100vh;
  background: #0A0A0A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background Blur Gradients */
.blur-gradient {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  pointer-events: none;
  z-index: 1;
}

.blur-gradient-1 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #CC6754 0%, #D19049 100%);
  top: 10%;
  left: -10%;
}

.blur-gradient-2 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #D19049 0%, #CC6754 100%);
  top: calc(50% - 239px/2 - 29.5px);
  left: calc(50% - 150px);
}

.blur-gradient-3 {
  width: 350px;
  height: 350px;
  background: linear-gradient(135deg, #CC6754 0%, #D19049 100%);
  top: 20%;
  right: 0px;
}

.blur-gradient-4 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #D19049 0%, #CC6754 100%);
  bottom: 20%;
  left: 20%;
}

.blur-gradient-5 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #CC6754 0%, #D19049 100%);
  bottom: 10%;
  right: -10%;
}

/* Main Content */
.user-dashboard-main {
  position: relative;
  z-index: 2;
  padding-top: 100px;
  min-height: calc(100vh - 100px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-dashboard-main {
    padding-top: 80px;
    min-height: calc(100vh - 80px);
  }

  .blur-gradient-1 {
    width: 250px;
    height: 250px;
    top: 5%;
    left: -20%;
  }

  .blur-gradient-2 {
    width: 200px;
    height: 200px;
    top: 30%;
    left: calc(50% - 100px);
  }

  .blur-gradient-3 {
    width: 220px;
    height: 220px;
    top: 15%;
    right: -10%;
  }

  .blur-gradient-4 {
    width: 180px;
    height: 180px;
    bottom: 25%;
    left: 10%;
  }

  .blur-gradient-5 {
    width: 200px;
    height: 200px;
    bottom: 5%;
    right: -15%;
  }
}
