/* User SideNav Styles */
.user-sidenav-container {
  width: 100%;
  margin-bottom: 24px;
}

.user-sidenav-list {
  display: flex;
  flex-direction: row;
  gap: 16px;
  background: #171717;
  border-radius: 12px;
  padding: 4px;
  width: 480px;
  height: 48px;
  align-items: center;
  justify-content: center;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.user-sidenav-list::-webkit-scrollbar {
  display: none;
}

.user-sidenav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #A3A3A3;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.user-sidenav-item:hover {
  background: #404040;
  border: 1px solid #525252;
  color: #FFFFFF;
}

.user-sidenav-item.active {
  background: #BF4129;
  color: #FFFFFF;
  border: 1px solid #BF4129;
}

.user-sidenav-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.user-sidenav-label {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .user-sidenav-container {
    width: 100%;
    margin-bottom: 16px;
  }

  .user-sidenav-list {
    width: 353px;
    height: 48px;
    background: #262626;
    border-radius: 16px;
    padding: 4px;
    gap: 24px;
    justify-content: flex-start;
    overflow-x: auto;
    scroll-behavior: smooth;
  }

  .user-sidenav-item {
    background: #404040;
    border: 1px solid #525252;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
    min-width: 80px;
    justify-content: center;
  }

  .user-sidenav-item:hover {
    background: #525252;
  }

  .user-sidenav-item.active {
    background: #BF4129;
    border: 1px solid #BF4129;
  }

  .user-sidenav-icon {
    width: 14px;
    height: 14px;
  }

  .user-sidenav-label {
    font-size: 12px;
  }
}
