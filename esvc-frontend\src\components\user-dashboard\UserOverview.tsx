import React, { useState } from 'react';
import '../../styles/components/UserOverview.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
import trendUpIcon from '../../assets/trend-up.png';
import informationCircleIcon from '../../assets/information-circle.png';

interface UserOverviewProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const UserOverview: React.FC<UserOverviewProps> = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showBalances, setShowBalances] = useState(true);
  const [selectedWallet, setSelectedWallet] = useState('Wallet 1 ($10,000 stake)');

  const toggleBalances = () => {
    setShowBalances(!showBalances);
  };

  return (
    <UserDashboardLayout className="user-overview-container">
      <div className="user-overview-content">
        {/* Welcome Section */}
        <div className="welcome-section">
          <h1 className="welcome-title">Hi, Oluwatosin 👋</h1>
          <p className="welcome-subtitle">Here is your staking overview</p>
          
          <div className="balance-controls">
            <div className="balance-toggle">
              <span>Show balances</span>
              <label className="toggle-switch">
                <input 
                  type="checkbox" 
                  checked={showBalances} 
                  onChange={toggleBalances}
                />
                <span className="slider"></span>
              </label>
              <span>Hide balances</span>
            </div>
            <button className="stake-esvc-btn">
              <span className="stake-icon">🎯</span>
              Stake ESVC
            </button>
          </div>
        </div>

        <div className="user-dashboard-layout">
          {/* Sidebar */}
          <UserSideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Main Dashboard Content */}
          <div className="user-dashboard-content">
            {/* General Summary Card */}
            <div className="summary-card">
              <h2 className="card-title">General Summary (From Your 7 Wallets)</h2>
              
              <div className="summary-stats">
                <div className="stat-item">
                  <span className="stat-label">TOTAL ESVC STAKED</span>
                  <div className="stat-value">
                    {showBalances ? '788.50' : '***.**'}
                    <span className="stat-unit">ESVC</span>
                  </div>
                </div>
                
                <div className="stat-item">
                  <span className="stat-label">ROI EARNED SO FAR</span>
                  <div className="stat-value">
                    {showBalances ? '$700' : '$***'}
                  </div>
                </div>
                
                <div className="stat-item">
                  <span className="stat-label">CURRENT STAKED ESVC VALUE</span>
                  <div className="stat-value">
                    {showBalances ? '$13,700' : '$***,***'}
                  </div>
                </div>
                
                <div className="stat-item">
                  <span className="stat-label">ROI PAID SO FAR</span>
                  <div className="stat-value">
                    {showBalances ? '$660' : '$***'}
                  </div>
                </div>
              </div>
              
              <div className="today-stats">
                <span className="today-label">+ 4.8% Today</span>
              </div>
            </div>

            {/* Individual Wallet Summary */}
            <div className="wallet-summary-card">
              <h2 className="card-title">Individual Wallet Summary</h2>
              
              <div className="wallet-selector">
                <select 
                  value={selectedWallet} 
                  onChange={(e) => setSelectedWallet(e.target.value)}
                  className="wallet-dropdown"
                >
                  <option value="Wallet 1 ($10,000 stake)">Wallet 1 ($10,000 stake)</option>
                  <option value="Wallet 2 ($5,000 stake)">Wallet 2 ($5,000 stake)</option>
                  <option value="Wallet 3 ($2,500 stake)">Wallet 3 ($2,500 stake)</option>
                </select>
                
                <div className="wallet-info">
                  <img src={informationCircleIcon} alt="Info" className="info-icon" />
                  <span>Every new stake you make gets its own dedicated wallet for added transparency and traceability.</span>
                </div>
              </div>

              <div className="wallet-stats">
                <div className="wallet-stat-row">
                  <div className="wallet-stat">
                    <span className="stat-label">TOTAL ESVC STAKED</span>
                    <div className="stat-value">
                      {showBalances ? '788.50' : '***.**'}
                      <span className="stat-unit">ESVC</span>
                    </div>
                    <div className="stat-change positive">
                      <img src={trendUpIcon} alt="Up" className="trend-icon" />
                      + 4.8%
                    </div>
                  </div>
                  
                  <div className="wallet-stat">
                    <span className="stat-label">CURRENT STAKED ESVC VALUE</span>
                    <div className="stat-value">
                      {showBalances ? '$13,700' : '$***,***'}
                    </div>
                    <div className="stat-change positive">
                      <img src={trendUpIcon} alt="Up" className="trend-icon" />
                      + 4.8%
                    </div>
                    <div className="deposit-info">$10,000 at Deposit</div>
                  </div>
                </div>
                
                <div className="wallet-stat-row">
                  <div className="wallet-stat">
                    <span className="stat-label">ROI EARNED SO FAR</span>
                    <div className="stat-value">
                      {showBalances ? '$700' : '$***'}
                    </div>
                  </div>
                  
                  <div className="wallet-stat">
                    <span className="stat-label">ROI PAID SO FAR</span>
                    <div className="stat-value">
                      {showBalances ? '$660' : '$***'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* ROI Summary */}
            <div className="roi-summary-card">
              <h2 className="card-title">ROI Summary</h2>
              <div className="roi-available">$8.3 (1) ESVC Available</div>
              
              <div className="roi-progress">
                <div className="roi-bar">
                  <div className="roi-earned" style={{ width: '35%' }}></div>
                </div>
                <div className="roi-labels">
                  <span>$700 ROI earned so far</span>
                  <span>$2,000 Total Expected ROI</span>
                </div>
              </div>
              
              <button className="withdraw-btn">Withdraw Earned ROI</button>
              
              <div className="withdrawal-info">
                <img src={informationCircleIcon} alt="Info" className="info-icon" />
                <span>Minimum withdrawal amount is $10</span>
              </div>
            </div>

            {/* Unstake Section */}
            <div className="unstake-card">
              <h2 className="card-title">Unstake</h2>
              
              <div className="unstake-info">
                <div className="unstake-row">
                  <span className="unstake-label">DATE STAKED</span>
                  <span className="unstake-value">Jan 3, 2025</span>
                </div>
                <div className="unstake-row">
                  <span className="unstake-label">UNSTAKE DATE</span>
                  <span className="unstake-value">Jan 3, 2026</span>
                </div>
                <div className="unstake-row">
                  <span className="unstake-label">DAYS LEFT UNTIL UNSTAKE</span>
                  <span className="unstake-value">267 Days</span>
                </div>
              </div>
              
              <button className="unstake-btn" disabled>Unstake</button>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default UserOverview;
