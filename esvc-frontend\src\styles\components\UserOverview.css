/* User Overview Styles */
.user-overview-container {
  position: relative;
  z-index: 2;
}

.user-overview-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px;
}

/* Welcome Section */
.welcome-section {
  margin-bottom: 40px;
}

.welcome-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.welcome-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: #A3A3A3;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.balance-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #A3A3A3;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #BF4129;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #D14A2A;
}

.stake-icon {
  font-size: 16px;
}

/* Dashboard Layout */
.user-dashboard-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.user-dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Card Styles with Glassmorphism */
.summary-card,
.wallet-summary-card,
.roi-summary-card,
.unstake-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.card-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
  line-height: 1.4;
}

/* Summary Stats */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #A3A3A3;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  display: flex;
  align-items: baseline;
  gap: 8px;
  line-height: 1.2;
}

.stat-unit {
  font-size: 14px;
  font-weight: 500;
  color: #A3A3A3;
}

.today-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.today-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #10B981;
}

/* Wallet Summary */
.wallet-selector {
  margin-bottom: 24px;
}

.wallet-dropdown {
  width: 100%;
  background: rgba(64, 64, 64, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  margin-bottom: 12px;
  cursor: pointer;
}

.wallet-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #A3A3A3;
  line-height: 1.4;
}

.info-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.wallet-stats {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.wallet-stat-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.wallet-stat {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #10B981;
}

.trend-icon {
  width: 12px;
  height: 12px;
}

.deposit-info {
  font-family: 'Montserrat', sans-serif;
  font-size: 11px;
  color: #A3A3A3;
  margin-top: 4px;
}

/* ROI Summary */
.roi-available {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #F59E0B;
  margin-bottom: 16px;
}

.roi-progress {
  margin-bottom: 24px;
}

.roi-bar {
  width: 100%;
  height: 8px;
  background: rgba(64, 64, 64, 0.5);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.roi-earned {
  height: 100%;
  background: linear-gradient(90deg, #10B981 0%, #059669 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.roi-labels {
  display: flex;
  justify-content: space-between;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #A3A3A3;
}

.withdraw-btn {
  background: #F59E0B;
  color: #000000;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
  margin-bottom: 16px;
}

.withdraw-btn:hover {
  background: #D97706;
}

.withdrawal-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #3B82F6;
  line-height: 1.4;
}

/* Unstake Section */
.unstake-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.unstake-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unstake-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #A3A3A3;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unstake-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
}

.unstake-btn {
  background: rgba(64, 64, 64, 0.5);
  color: #A3A3A3;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 24px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-overview-content {
    padding: 24px 16px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }

  .balance-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .user-dashboard-layout {
    flex-direction: column;
    gap: 16px;
  }

  .summary-card,
  .wallet-summary-card,
  .roi-summary-card,
  .unstake-card {
    padding: 24px 16px;
    margin: 0;
  }

  .summary-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .wallet-stat-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-value {
    font-size: 20px;
  }

  .card-title {
    font-size: 18px;
  }
}
