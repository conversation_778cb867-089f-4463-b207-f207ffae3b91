import React, { useState } from 'react';
import '../../styles/components/UserHeader.css';
import logoEsvc from '../../assets/logo-esvc.png';

interface UserHeaderProps {
  onNavigateToLanding?: () => void;
}

const UserHeader: React.FC<UserHeaderProps> = ({
  onNavigateToLanding
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="user-header">
      <div className="container">
        <div className="user-header-content">
          {/* Logo */}
          <div className="logo" onClick={onNavigateToLanding} style={{ cursor: 'pointer' }}>
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="nav desktop-nav">
            <a href="#home" className="nav-link active">Home</a>
            <a href="#stake" className="nav-link">Stake ESVC</a>
            <a href="#funding" className="nav-link">Get Funding</a>
            <a href="#challenge" className="nav-link">Trade Challenge</a>
            <a href="#contact" className="nav-link">Contact Us</a>
          </nav>

          {/* Desktop User Profile */}
          <div className="user-header-actions desktop-actions">
            <div className="user-profile">
              <div className="user-avatar">
                <span>AO</span>
              </div>
            </div>
          </div>

          {/* Mobile Hamburger Menu */}
          <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`mobile-menu ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`}>
          <nav className="mobile-nav">
            <a href="#home" className="mobile-nav-link">Home</a>
            <a href="#stake" className="mobile-nav-link">Stake ESVC</a>
            <a href="#funding" className="mobile-nav-link">Get Funding</a>
            <a href="#challenge" className="mobile-nav-link">Trade Challenge</a>
            <a href="#contact" className="mobile-nav-link">Contact Us</a>
          </nav>
          <div className="mobile-actions">
            <div className="user-profile mobile-profile">
              <div className="user-avatar">
                <span>AO</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default UserHeader;
