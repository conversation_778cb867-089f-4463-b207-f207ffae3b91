/* User Header Styles */
.user-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 0;
}

.user-header .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.user-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-image {
  height: 40px;
  width: auto;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  color: #A3A3A3;
  text-decoration: none;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.3s ease;
  line-height: 1.6;
}

.nav-link:hover,
.nav-link.active {
  color: #FFFFFF;
}

/* User Profile */
.user-header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #BF4129;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.user-avatar:hover {
  background: #D14A2A;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background: #FFFFFF;
  transition: all 0.3s ease;
}

/* Mobile Menu */
.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 353px;
  background: #262626;
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-open {
  opacity: 1;
  visibility: visible;
}

.mobile-nav {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.mobile-nav-link {
  color: #A3A3A3;
  text-decoration: none;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 16px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
  line-height: 1.6;
}

.mobile-nav-link:hover {
  background: #525252;
  color: #FFFFFF;
}

.mobile-actions {
  display: flex;
  justify-content: center;
}

.mobile-profile {
  justify-content: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-header {
    padding: 12px 0;
  }

  .user-header .container {
    padding: 0 16px;
  }

  .user-header-content {
    height: 50px;
  }

  .logo-image {
    height: 32px;
  }

  .desktop-nav,
  .desktop-actions {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .mobile-menu {
    display: block;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    font-size: 12px;
  }
}
